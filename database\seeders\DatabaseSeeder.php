<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Seed pages, categories, products and FAQs
        $this->call([
            PageSeeder::class,
            CategorySeeder::class,
            ProductSeeder::class,
            FaqSeeder::class,
        ]);
    }
}
